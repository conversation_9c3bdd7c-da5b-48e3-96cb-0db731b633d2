#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 parse_first_option 函数的脚本
"""

import sys
import os
sys.path.append('/home/<USER>/evalscope')

from evalscope.utils.utils import Response<PERSON><PERSON>er

def test_parse_first_option():
    """测试 parse_first_option 函数"""
    
    print("=" * 60)
    print("开始测试 parse_first_option 函数")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        # 英文格式测试
        "Answer: A",
        "ANSWER: B", 
        "The answer is C",
        "The correct answer is: D",
        "The correct answer is:\nA",
        "The correct answer is:\n\n- B",
        "The answer might be:\n\n- C",
        "answer is (D)",
        "answer is A",
        
        # 中文格式测试（虽然函数主要针对英文）
        "答案是A",
        "正确答案是B",
        
        # 复杂文本测试
        "Based on the analysis above, the answer is A. This is because...",
        "After careful consideration, I believe the correct answer is: B",
        "Looking at all the options, answer is (C)",
        
        # 无匹配测试
        "这是一段没有答案的文本",
        "No clear answer here",
        "",
        
        # 边界情况测试
        "Multiple answers: A, B, C, but the answer is D",
        "answer is 123",  # 数字答案
        "answer is ABC",  # 多字符答案
    ]
    
    print(f"总共 {len(test_cases)} 个测试用例\n")
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n{'='*50}")
        print(f"测试用例 {i}:")
        print(f"输入: {repr(test_text)}")
        print(f"{'='*50}")
        
        try:
            result = ResponseParser.parse_first_option(test_text)
            print(f"结果: {repr(result)}")
            if result:
                print(f"✓ 成功提取到答案: {result}")
            else:
                print("✗ 未提取到答案")
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        print("-" * 50)

def test_with_real_model_output():
    """测试真实模型输出的情况"""
    print("\n" + "=" * 60)
    print("测试真实模型输出场景")
    print("=" * 60)
    
    # 模拟一些真实的模型输出
    real_outputs = [
        """Based on the given information, I need to analyze each option carefully.

Looking at option A: This seems incorrect because...
Looking at option B: This could be possible but...
Looking at option C: This appears to be the most logical choice because...
Looking at option D: This doesn't align with the facts.

Therefore, the correct answer is: C""",

        """Let me think through this step by step.

First, I'll consider the context...
Second, I'll evaluate each possibility...
Finally, I can conclude that the answer is B.""",

        """After analyzing all the given options and considering the context, I believe the most appropriate answer would be A. This is supported by the evidence presented in the question.""",
    ]
    
    for i, output in enumerate(real_outputs, 1):
        print(f"\n{'='*40}")
        print(f"真实输出测试 {i}:")
        print(f"{'='*40}")
        print(f"输出长度: {len(output)} 字符")
        print(f"输出预览: {output[:100]}...")
        
        try:
            result = ResponseParser.parse_first_option(output)
            print(f"\n提取结果: {repr(result)}")
            if result:
                print(f"✓ 成功提取到答案: {result}")
            else:
                print("✗ 未提取到答案")
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    print("开始测试 parse_first_option 函数...")
    
    # 基础测试
    test_parse_first_option()
    
    # 真实场景测试
    test_with_real_model_output()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)
